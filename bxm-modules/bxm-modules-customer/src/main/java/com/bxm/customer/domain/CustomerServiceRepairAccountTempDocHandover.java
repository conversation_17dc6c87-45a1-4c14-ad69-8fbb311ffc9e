package com.bxm.customer.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.bxm.common.core.annotation.Excel;
import com.bxm.common.core.web.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 材料、资料交接对象 c_customer_service_doc_handover
 *
 * <AUTHOR>
 * @date 2024-07-05
 */
@Data
@ApiModel("补账的临时材料交接")
@Accessors(chain = true)
@TableName("c_customer_service_repair_account_temp_doc_handover")
public class CustomerServiceRepairAccountTempDocHandover extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @TableId(value = "id", type = IdType.AUTO)
    @Excel(name = "主键ID")
    @ApiModelProperty(value = "主键ID")
    private Long id;

    /** 关联id */
    @Excel(name = "关联id")
    @TableField("customer_service_repair_account_id")
    @ApiModelProperty(value = "关联id")
    private Long customerServiceRepairAccountId;

    /** 账期 */
    @Excel(name = "账期")
    @TableField("period")
    @ApiModelProperty(value = "账期")
    private Integer period;

    /** 客户服务ID */
    @Excel(name = "客户服务ID")
    @TableField("customer_service_id")
    @ApiModelProperty(value = "客户服务ID")
    private Long customerServiceId;

    /** 客户名 */
    @Excel(name = "客户名")
    @TableField("customer_name")
    @ApiModelProperty(value = "客户名")
    private String customerName;

    /** 信用代码 */
    @Excel(name = "信用代码")
    @TableField("credit_code")
    @ApiModelProperty(value = "信用代码")
    private String creditCode;

    /** 标题 */
    @Excel(name = "标题")
    @TableField("title")
    @ApiModelProperty(value = "标题")
    private String title;

    /** 批次序号 */
    @Excel(name = "批次序号")
    @TableField("batch_num")
    @ApiModelProperty(value = "批次序号")
    private Integer batchNum;

    /** 状态: */
    @Excel(name = "状态:")
    @TableField("status")
    @ApiModelProperty(value = "状态:")
    private Integer status;

    /** 凭票入账:1-是/0-否 */
    @Excel(name = "凭票入账:1-是/0-否")
    @TableField("is_voucher_entry")
    @ApiModelProperty(value = "凭票入账:1-是/0-否")
    private Integer isVoucherEntry;

    /** 备注 */
    @Excel(name = "备注")
    @TableField("remark")
    @ApiModelProperty(value = "备注")
    private String remark;

    /** 提交时间 */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "提交时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @TableField("submit_time")
    @ApiModelProperty(value = "提交时间")
    private LocalDateTime submitTime;

    /** 提交员工部门ID */
    @Excel(name = "提交员工部门ID")
    @TableField("submit_employee_dept_id")
    @ApiModelProperty(value = "提交员工部门ID")
    private Long submitEmployeeDeptId;

    /** 提交员工部门名称 */
    @Excel(name = "提交员工部门名称")
    @TableField("submit_employee_dept_name")
    @ApiModelProperty(value = "提交员工部门名称")
    private String submitEmployeeDeptName;

    /** 提交员工id */
    @Excel(name = "提交员工id")
    @TableField("submit_employee_id")
    @ApiModelProperty(value = "提交员工id")
    private Long submitEmployeeId;

    /** 提交员工名称 */
    @Excel(name = "提交员工名称")
    @TableField("submit_employee_name")
    @ApiModelProperty(value = "提交员工名称")
    private String submitEmployeeName;

    /** 承验人类型，默认是总部：1总部，-1会计区域 */
    @Excel(name = "承验人类型，默认是总部：1总部，-1会计区域")
    @TableField("verification_employee_type")
    @ApiModelProperty(value = "承验人类型，默认是总部：1总部，-1会计区域")
    private Integer verificationEmployeeType;

    /** 核验时间 */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "核验时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @TableField("verification_time")
    @ApiModelProperty(value = "核验时间")
    private LocalDateTime verificationTime;

    /** 核验员工部门ID */
    @Excel(name = "核验员工部门ID")
    @TableField("verification_employee_dept_id")
    @ApiModelProperty(value = "核验员工部门ID")
    private Long verificationEmployeeDeptId;

    /** 核验员工部门名称 */
    @Excel(name = "核验员工部门名称")
    @TableField("verification_employee_dept_name")
    @ApiModelProperty(value = "核验员工部门名称")
    private String verificationEmployeeDeptName;

    /** 核验员工id */
    @Excel(name = "核验员工id")
    @TableField("verification_employee_id")
    @ApiModelProperty(value = "核验员工id")
    private Long verificationEmployeeId;

    /** 核验员工名称 */
    @Excel(name = "核验员工名称")
    @TableField("verification_employee_name")
    @ApiModelProperty(value = "核验员工名称")
    private String verificationEmployeeName;

    /** 完整度：1-已完整、2-缺但齐、3-有缺失待补充 */
    @Excel(name = "完整度：1-已完整、2-缺但齐、3-有缺失待补充")
    @TableField("whole_level")
    @ApiModelProperty(value = "完整度：1-已完整、2-缺但齐、3-有缺失待补充")
    private Integer wholeLevel;

    /** 核验说明 */
    @Excel(name = "核验说明")
    @TableField("verification_remark")
    @ApiModelProperty(value = "核验说明")
    private String verificationRemark;

    /** 退回时间 */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "退回时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @TableField("back_time")
    @ApiModelProperty(value = "退回时间")
    private LocalDateTime backTime;

    /** 退回员工部门ID */
    @Excel(name = "退回员工部门ID")
    @TableField("back_employee_dept_id")
    @ApiModelProperty(value = "退回员工部门ID")
    private Long backEmployeeDeptId;

    /** 退回员工部门名称 */
    @Excel(name = "退回员工部门名称")
    @TableField("back_employee_dept_name")
    @ApiModelProperty(value = "退回员工部门名称")
    private String backEmployeeDeptName;

    /** 退回员工id */
    @Excel(name = "退回员工id")
    @TableField("back_employee_id")
    @ApiModelProperty(value = "退回员工id")
    private Long backEmployeeId;

    /** 退回员工名称 */
    @Excel(name = "退回员工名称")
    @TableField("back_employee_name")
    @ApiModelProperty(value = "退回员工名称")
    private String backEmployeeName;

    /** 退回说明 */
    @Excel(name = "退回说明")
    @TableField("back_remark")
    @ApiModelProperty(value = "退回说明")
    private String backRemark;

    /** 有无 税号发票:1-有/0-无 */
    @Excel(name = "有无 税号发票:1-有/0-无")
    @TableField("has_tax_ticket")
    @ApiModelProperty(value = "有无 税号发票:1-有/0-无")
    private Integer hasTaxTicket;

    /** 有无 其他票据:1-有/0-无 */
    @Excel(name = "有无 其他票据:1-有/0-无")
    @TableField("has_other_ticket")
    @ApiModelProperty(value = "有无 其他票据:1-有/0-无")
    private Integer hasOtherTicket;

}
