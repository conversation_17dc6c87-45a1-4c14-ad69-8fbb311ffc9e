package com.bxm.customer.helper;

import com.bxm.customer.domain.ValueAddedEmployee;
import com.bxm.customer.domain.enums.ValueAddedOperationType;
import com.bxm.customer.domain.vo.valueAdded.SocialInsuranceVO;
import com.bxm.customer.domain.vo.valueAdded.ValueAddedEmployeeVO;
import com.bxm.customer.service.IValueAddedEmployeeService;
import com.bxm.customer.utils.ValidateUtil;
import com.bxm.common.core.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

/**
 * 增值员工校验助手类
 *
 * 提供完整的员工信息校验功能，包括：
 * 1. 基础字段格式校验（姓名、身份证号、手机号等）
 * 2. 业务规则校验（申报基数、备注等）
 * 3. 唯一性校验（同一交付单内身份证号、手机号唯一）
 * 4. 操作类型相关校验
 *
 * <AUTHOR>
 * @date 2025-01-21
 */
@Slf4j
@Component
public class ValueAddedEmpValidationHelper {

    @Autowired
    private IValueAddedEmployeeService valueAddedEmployeeService;


    // ==================== 员工相关校验方法 ====================

    /**
     * 校验员工姓名
     * 规则：必填，2-50个字，至少填2个字
     *
     * @param employeeName 员工姓名
     * @throws IllegalArgumentException 当校验失败时抛出
     */
    public static void validateEmployeeName(String employeeName) {
        if (StringUtils.isEmpty(employeeName)) {
            throw new IllegalArgumentException("员工姓名不能为空");
        }

        String trimmedName = employeeName.trim();
        if (trimmedName.length() < 2) {
            throw new IllegalArgumentException("员工姓名至少需要2个字符");
        }

        if (trimmedName.length() > 50) {
            throw new IllegalArgumentException("员工姓名不能超过50个字符");
        }
    }

    // ==================== 基础字段格式校验方法（委托给ValidateUtil） ====================

    /**
     * 校验身份证号（只校验是否为空）
     * 规则：必填，不能为空
     *
     * @param idNumber 身份证号
     * @throws IllegalArgumentException 当校验失败时抛出
     */
    public static void validateIdNumberStrict(String idNumber) {
        ValidateUtil.validateIdNumberStrict(idNumber);
    }

    /**
     * 校验身份证号（只校验是否为空）
     *
     * @param idNumber 身份证号
     * @param allowEmpty 是否允许为空
     * @return 验证结果
     */
    public static boolean isValidIdNumber(String idNumber, boolean allowEmpty) {
        return ValidateUtil.isValidIdNumber(idNumber, allowEmpty);
    }

    /**
     * 校验身份证号（只校验是否为空，不允许为空）
     *
     * @param idNumber 身份证号
     * @return 验证结果
     */
    public static boolean isValidIdNumber(String idNumber) {
        return ValidateUtil.isValidIdNumber(idNumber);
    }

    /**
     * 验证身份证号并抛出异常（只校验是否为空）
     *
     * @param idNumber 身份证号
     * @param fieldName 字段名称，用于错误信息
     * @throws IllegalArgumentException 当身份证号为空时抛出
     */
    public static void validateIdNumberAndThrow(String idNumber, String fieldName) {
        ValidateUtil.validateIdNumberAndThrow(idNumber, fieldName);
    }

    /**
     * 验证身份证号并抛出异常（使用默认字段名，只校验是否为空）
     *
     * @param idNumber 身份证号
     * @throws IllegalArgumentException 当身份证号为空时抛出
     */
    public static void validateIdNumberAndThrow(String idNumber) {
        ValidateUtil.validateIdNumberAndThrow(idNumber);
    }

    /**
     * 校验手机号格式
     * 规则：必填，11个数字
     *
     * @param mobile 手机号
     * @throws IllegalArgumentException 当校验失败时抛出
     */
    public static void validateMobile(String mobile) {
        ValidateUtil.validateMobile(mobile);
    }

    // ==================== 唯一性校验方法 ====================

    /**
     * 检查同一交付单内身份证号的唯一性
     *
     * @param deliveryOrderNo 交付单编号
     * @param idNumber 身份证号
     * @param bizType 业务类型
     * @param excludeId 排除的员工ID（用于更新时排除自己）
     * @return 是否唯一（true表示唯一，false表示重复）
     */
    public boolean checkIdNumberUniquenessInDeliveryOrder(String deliveryOrderNo, String idNumber,
                                                         Integer bizType, Long excludeId) {
        if (StringUtils.isEmpty(deliveryOrderNo) || StringUtils.isEmpty(idNumber)) {
            return true; // 空值不参与唯一性校验
        }

        // 通过Service层查询现有员工
        ValueAddedEmployee existingEmployee = valueAddedEmployeeService.getByDeliveryOrderAndIdNumber(
                deliveryOrderNo, idNumber.trim(), bizType);

        // 如果没有找到重复记录，则唯一
        if (existingEmployee == null) {
            return true;
        }

        // 如果是更新操作且找到的是自己，则唯一
        if (excludeId != null && excludeId.equals(existingEmployee.getId())) {
            return true;
        }

        // 其他情况都是重复
        log.warn("Duplicate ID number found in delivery order: deliveryOrderNo={}, idNumber={}, bizType={}",
                deliveryOrderNo, idNumber, bizType);
        return false;
    }

    /**
     * 检查同一交付单内手机号的唯一性
     *
     * @param deliveryOrderNo 交付单编号
     * @param mobile 手机号
     * @param bizType 业务类型
     * @param excludeId 排除的员工ID（用于更新时排除自己）
     * @return 是否唯一（true表示唯一，false表示重复）
     */
    public boolean checkMobileUniquenessInDeliveryOrder(String deliveryOrderNo, String mobile,
                                                       Integer bizType, Long excludeId) {
        if (StringUtils.isEmpty(deliveryOrderNo) || StringUtils.isEmpty(mobile)) {
            return true; // 空值不参与唯一性校验
        }

        // 通过Service层查询现有员工
        ValueAddedEmployee existingEmployee = valueAddedEmployeeService.getByDeliveryOrderAndMobile(
                deliveryOrderNo, mobile.trim(), bizType);

        // 如果没有找到重复记录，则唯一
        if (existingEmployee == null) {
            return true;
        }

        // 如果是更新操作且找到的是自己，则唯一
        if (excludeId != null && excludeId.equals(existingEmployee.getId())) {
            return true;
        }

        // 其他情况都是重复
        log.warn("Duplicate mobile found in delivery order: deliveryOrderNo={}, mobile={}, bizType={}",
                deliveryOrderNo, mobile, bizType);
        return false;
    }

    // ==================== 业务规则校验方法 ====================

    /**
     * 校验申报基数
     * 规则：方式为增员或更正时必填，浮点2位数字
     *
     * @param socialInsuranceBase 申报基数
     * @param operationType 操作类型
     * @throws IllegalArgumentException 当校验失败时抛出
     */
    public static void validateSocialInsuranceBase(BigDecimal socialInsuranceBase, Integer operationType) {
        // 检查是否为增员或更正操作
        if (isAddOrCorrectionOperation(operationType)) {
            if (socialInsuranceBase == null) {
                throw new IllegalArgumentException("方式为增员或更正时，申报基数不能为空");
            }

            if (socialInsuranceBase.compareTo(BigDecimal.ZERO) < 0) {
                throw new IllegalArgumentException("申报基数不能为负数");
            }

            // 检查小数位数不超过2位
            if (socialInsuranceBase.scale() > 2) {
                throw new IllegalArgumentException("申报基数最多保留2位小数");
            }
        }
    }

    /**
     * 校验备注
     * 规则：当选择申报险种为"其他"时，备注必填
     * 注意：SocialInsurance字段已改为非必填，此方法保留用于其他业务场景的备注校验
     *
     * @param remark 备注
     * @param qiTaSelected 是否选择了"其他"险种
     * @throws IllegalArgumentException 当校验失败时抛出
     */
    public static void validateRemark(String remark, Boolean qiTaSelected) {
        // 1. 当明确选择了"其他"险种时，备注必填
        if (qiTaSelected != null && qiTaSelected) {
            if (StringUtils.isEmpty(remark)) {
                throw new IllegalArgumentException("当选择申报险种为其他时，备注不能为空");
            }
        }
    }

    /**
     * 校验备注（兼容旧版本方法，已废弃）
     * @deprecated 使用 validateRemark(String remark, Boolean qiTaSelected) 替代
     */
    @Deprecated
    public static void validateRemark(String remark, SocialInsuranceVO socialInsurance) {
        // 2. 兼容旧版本调用，SocialInsurance字段已改为非必填，不再进行校验
        // 如果需要校验"其他"险种的备注，请使用新的方法签名
    }

    /**
     * 校验同一交付单内的唯一性
     * 规则：同一交付单内身份证号唯一，手机号唯一
     *
     * @param employeeVO 员工信息VO
     * @param bizType 业务类型
     * @throws IllegalArgumentException 当校验失败时抛出
     */
    public void validateUniqueness(ValueAddedEmployeeVO employeeVO, Integer bizType) {
        // 校验身份证号唯一性
        if (!checkIdNumberUniquenessInDeliveryOrder(
                employeeVO.getDeliveryOrderNo(),
                employeeVO.getIdNumber(),
                bizType,
                employeeVO.getId())) {
            throw new IllegalArgumentException("同一交付单内身份证号不能重复");
        }

        // 校验手机号唯一性
        if (!checkMobileUniquenessInDeliveryOrder(
                employeeVO.getDeliveryOrderNo(),
                employeeVO.getMobile(),
                bizType,
                employeeVO.getId())) {
            throw new IllegalArgumentException("同一交付单内手机号不能重复");
        }
    }

    // ==================== 操作类型相关方法 ====================

    /**
     * 判断操作类型是否为增员或更正
     *
     * @param operationType 操作类型
     * @return 是否为增员或更正操作
     */
    public static boolean isAddOrCorrectionOperation(Integer operationType) {
        if (operationType == null) {
            return false;
        }
        // 根据ValueAddedOperationType枚举定义：
        // 1-提醒，2-更正，3-减员
        // 根据业务需求，"增员"操作对应枚举中的"提醒"(1)，"更正"操作对应枚举中的"更正"(2)
        // 当操作类型为提醒或更正时，需要校验申报基数和申报险种
        return operationType.equals(ValueAddedOperationType.REMIND.getCode()) ||
               operationType.equals(ValueAddedOperationType.CORRECTION.getCode());
    }

    /**
     * 判断操作类型是否为减员
     *
     * @param operationType 操作类型
     * @return 是否为减员操作
     */
    public static boolean isReductionOperation(Integer operationType) {
        if (operationType == null) {
            return false;
        }
        // 3-减员：减员操作不需要校验申报基数、申报险种等业务规则
        return operationType.equals(ValueAddedOperationType.REDUCTION.getCode());
    }

    /**
     * 校验申报基数的前置条件（保留兼容性）
     *
     * @param operationType 操作类型
     * @param socialInsuranceBase 申报基数
     * @throws IllegalArgumentException 当校验失败时抛出
     */
    public static void validateSocialInsuranceBaseCondition(Integer operationType, Object socialInsuranceBase) {
        if (isAddOrCorrectionOperation(operationType)) {
            if (socialInsuranceBase == null) {
                throw new IllegalArgumentException("方式为增员或更正时，申报基数不能为空");
            }
        }
    }
}
