package com.bxm.customer.helper;

import com.bxm.customer.domain.ValueAddedDeliveryOrder;
import com.bxm.customer.domain.ValueAddedItemType;
import com.bxm.customer.domain.dto.valueAdded.BaseImportExcelDTO;
import com.bxm.customer.domain.dto.valueAdded.ImportValidationErrorDTO;
import com.bxm.customer.domain.dto.valueAdded.ImportValidationResult;
import com.bxm.customer.service.IValueAddedDeliveryOrderService;
import com.bxm.customer.service.IValueAddedItemTypeService;
import com.bxm.common.core.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 导入校验工具类
 *
 * 提供统一的批量校验能力，包括：
 * 1. 基础校验：表格格式、唯一性校验
 * 2. 业务校验：交付单存在性、前置条件校验
 * 3. 批量数据查询：交付单、增值事项类型
 *
 * <AUTHOR>
 * @date 2025-08-26
 */
@Slf4j
@Component
public class ValueAddedImportValidationHelper {

    @Autowired
    private IValueAddedDeliveryOrderService deliveryOrderService;

    @Autowired
    private IValueAddedItemTypeService itemTypeService;

    /**
     * 执行基础校验
     *
     * 校验内容：
     * 1. 表内交付单编号唯一性
     * 2. 表格基本格式校验（非空字段等）
     *
     * @param dataList Excel解析的数据列表
     * @return 校验结果
     */
    public ImportValidationResult performBasicValidation(List<? extends BaseImportExcelDTO> dataList) {

        List<ImportValidationErrorDTO> errors = new ArrayList<>();
        List<BaseImportExcelDTO> validData = new ArrayList<>();

        // 1. 校验表内交付单编号唯一性
        validateDeliveryOrderNoUniq(dataList, errors);

        // 2. 校验基本格式
        for (BaseImportExcelDTO data : dataList) {
            List<String> rowErrors = new ArrayList<>();
            // 校验必填字段
            validateRequiredFields(data, rowErrors);
            if (rowErrors.isEmpty()) {
                validData.add(data);
            } else {
                // 为每个错误创建一个错误记录
                for (String error : rowErrors) {
                    errors.add(ImportValidationErrorDTO.create(
                            data.getRowNumber(),
                            data.getDeliveryOrderNo(),
                            data.getCustomerName(),
                            data.getCreditCode(),
                            error,
                            "基础校验错误"
                    ));
                }
            }
        }

        if (errors.isEmpty()) {
            return ImportValidationResult.success(validData, null, null);
        } else {
            return ImportValidationResult.partial(validData, errors, null, null);
        }
    }

    /**
     * 执行业务校验
     *
     * 校验内容：
     * 1. 交付单编号是否存在
     * 2. 是否满足前置条件（根据具体业务类型）
     *
     * @param dataList 通过基础校验的数据列表
     * @param operationType 操作类型，用于确定前置条件校验逻辑
     * @return 校验结果，包含批量查询的交付单和增值事项类型数据
     */
    public ImportValidationResult performBusinessValidation(List<? extends BaseImportExcelDTO> dataList,
                                                           String operationType) {

        List<ImportValidationErrorDTO> errors = new ArrayList<>();
        List<BaseImportExcelDTO> validData = new ArrayList<>();

        // 1. 提取所有交付单编号
        List<String> deliveryOrderNos = dataList.stream()
                .map(BaseImportExcelDTO::getDeliveryOrderNo)
                .filter(StringUtils::isNotEmpty)
                .distinct()
                .collect(Collectors.toList());

        // 2. 批量查询交付单
        Map<String, ValueAddedDeliveryOrder> deliveryOrderMap = batchQueryDeliveryOrders(deliveryOrderNos);

        // 3. 批量查询增值事项类型
        Set<Integer> itemTypeIds = deliveryOrderMap.values().stream()
                .map(ValueAddedDeliveryOrder::getValueAddedItemTypeId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        Map<Integer, ValueAddedItemType> itemTypeMap = batchQueryItemTypes(itemTypeIds);

        // 4. 逐行进行业务校验
        for (BaseImportExcelDTO data : dataList) {
            List<String> rowErrors = new ArrayList<>();

            // 校验交付单是否存在
            ValueAddedDeliveryOrder order = deliveryOrderMap.get(data.getDeliveryOrderNo());
            if (order == null) {
                rowErrors.add("交付单不存在: " + data.getDeliveryOrderNo());
            } else {
                // 校验前置条件
                validateBusinessConditions(data, order, itemTypeMap.get(order.getValueAddedItemTypeId()),operationType, rowErrors);
            }

            if (rowErrors.isEmpty()) {
                validData.add(data);
            } else {
                // 为每个错误创建一个错误记录
                for (String error : rowErrors) {
                    errors.add(ImportValidationErrorDTO.create( data.getRowNumber(),data.getDeliveryOrderNo(),
                            data.getCustomerName(),data.getCreditCode(),error,"业务校验错误"
                    ));
                }
            }
        }

        if (errors.isEmpty()) {
            return ImportValidationResult.success(validData, deliveryOrderMap, itemTypeMap);
        } else {
            return ImportValidationResult.partial(validData, errors, deliveryOrderMap, itemTypeMap);
        }
    }

    /**
     * 校验交付单编号唯一性
     */
    private void validateDeliveryOrderNoUniq(List<? extends BaseImportExcelDTO> dataList,
                                             List<ImportValidationErrorDTO> errors) {
        Map<String, List<BaseImportExcelDTO>> duplicateMap = dataList.stream()
                .filter(data -> StringUtils.isNotEmpty(data.getDeliveryOrderNo()))
                .collect(Collectors.groupingBy(BaseImportExcelDTO::getDeliveryOrderNo));

        duplicateMap.entrySet().stream()
                .filter(entry -> entry.getValue().size() > 1)
                .forEach(entry -> {
                    String orderNo = entry.getKey();
                    List<BaseImportExcelDTO> duplicates = entry.getValue();

                    for (BaseImportExcelDTO data : duplicates) {
                        errors.add(ImportValidationErrorDTO.create(
                                data.getRowNumber(),
                                data.getDeliveryOrderNo(),
                                data.getCustomerName(),
                                data.getCreditCode(),
                                "表内交付单编号重复: " + orderNo,
                                "唯一性校验错误"
                        ));
                    }
                });
    }

    /**
     * 校验必填字段
     */
    private void validateRequiredFields(BaseImportExcelDTO data, List<String> rowErrors) {
        if (StringUtils.isEmpty(data.getDeliveryOrderNo())) {
            rowErrors.add("交付单编号不能为空");
        }
        if (StringUtils.isEmpty(data.getCustomerName())) {
            rowErrors.add("企业名不能为空");
        }
        if (StringUtils.isEmpty(data.getCreditCode())) {
            rowErrors.add("信用代码不能为空");
        }
    }

    /**
     * 批量查询交付单
     */
    private Map<String, ValueAddedDeliveryOrder> batchQueryDeliveryOrders(List<String> deliveryOrderNos) {
        if (deliveryOrderNos.isEmpty()) {
            return new HashMap<>();
        }

        log.info("批量查询交付单，数量: {}", deliveryOrderNos.size());
        List<ValueAddedDeliveryOrder> orders = deliveryOrderService.listByDeliveryOrderNos(deliveryOrderNos);

        return orders.stream()
                .collect(Collectors.toMap(
                        ValueAddedDeliveryOrder::getDeliveryOrderNo,
                        Function.identity(),
                        (existing, replacement) -> existing
                ));
    }

    /**
     * 批量查询增值事项类型
     */
    private Map<Integer, ValueAddedItemType> batchQueryItemTypes(Set<Integer> itemTypeIds) {
        if (itemTypeIds.isEmpty()) {
            return new HashMap<>();
        }

        log.info("批量查询增值事项类型，数量: {}", itemTypeIds.size());
        List<ValueAddedItemType> itemTypes = itemTypeService.listByIds(itemTypeIds);

        return itemTypes.stream()
                .collect(Collectors.toMap(
                        itemType -> itemType.getId().intValue(),
                        Function.identity(),
                        (existing, replacement) -> existing
                ));
    }

    /**
     * 校验业务条件（具体的前置条件校验逻辑）
     *
     * @param data Excel数据
     * @param order 交付单对象
     * @param itemType 增值事项类型对象
     * @param operationType 操作类型
     * @param rowErrors 错误列表
     */
    private void validateBusinessConditions(BaseImportExcelDTO data,
                                          ValueAddedDeliveryOrder order,
                                          ValueAddedItemType itemType,
                                          String operationType,
                                          List<String> rowErrors) {
        // 基础的数据一致性校验
        if (!data.getCustomerName().equals(order.getCustomerName())) {
            rowErrors.add("企业名与交付单中的企业名不一致");
        }

        if (!data.getCreditCode().equals(order.getCreditCode())) {
            rowErrors.add("信用代码与交付单中的信用代码不一致");
        }

        if (itemType == null) {
            rowErrors.add("交付单的增值事项类型信息缺失");
            return;
        }

        // 根据操作类型进行特定的业务校验
        if ("DELIVERY".equals(operationType)) {
            validateDeliverySpecificConditions(data, order, itemType, rowErrors);
        }
        // 其他操作类型的校验可以在这里添加
    }

    /**
     * 校验交付操作特有的业务条件
     */
    private void validateDeliverySpecificConditions(BaseImportExcelDTO baseData,
                                                   ValueAddedDeliveryOrder order,
                                                   ValueAddedItemType itemType,
                                                   List<String> rowErrors) {
        // 确保是交付操作的数据类型
        if (!(baseData instanceof com.bxm.customer.domain.dto.valueAdded.DeliveryImportExcelDTO)) {
            return;
        }

        com.bxm.customer.domain.dto.valueAdded.DeliveryImportExcelDTO data =
            (com.bxm.customer.domain.dto.valueAdded.DeliveryImportExcelDTO) baseData;

        String itemTypeValue = itemType.getItemType();
        String itemCode = itemType.getItemCode();

        // 校验1：itemType为TAX，交付结果是正常时，扣缴金额必填
        if ("TAX".equals(itemTypeValue)) {
            if ("正常".equals(data.getDeliveryResult())) {
                if (data.getTotalWithholdingAmount() == null ||
                    data.getTotalWithholdingAmount().compareTo(java.math.BigDecimal.ZERO) <= 0) {
                    rowErrors.add("TAX类型且交付结果为正常时，总扣缴金额必须大于0");
                }
            }
        }

        // 校验2：itemType为ACCOUNTING时可选，但itemCode为ACC_INVENTORY时库存表文件名必填
        if ("ACCOUNTING".equals(itemTypeValue)) {
            if ("ACC_INVENTORY".equals(itemCode)) {
                if (StringUtils.isEmpty(data.getInventoryFileName())) {
                    rowErrors.add("ACCOUNTING类型且事项编码为ACC_INVENTORY时，库存表文件名必填");
                }
            }
        }
    }
}
